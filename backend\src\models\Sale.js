const { executeQuery } = require('../config/database');

class Sale {
  constructor(data = {}) {
    this.id = data.id;
    this.saleNumber = data.sale_number || data.saleNumber;
    this.customerId = data.customer_id || data.customerId;
    this.customerName = data.customer_name || data.customerName;
    this.customerEmail = data.customer_email || data.customerEmail;
    this.customerPhone = data.customer_phone || data.customerPhone;
    this.totalAmount = data.total_amount || data.totalAmount;
    this.taxAmount = data.tax_amount || data.taxAmount;
    this.discountAmount = data.discount_amount || data.discountAmount;
    this.paymentMethod = data.payment_method || data.paymentMethod;
    this.paymentStatus = data.payment_status || data.paymentStatus;
    this.salesPerson = data.sales_person || data.salesPerson;
    this.notes = data.notes;
    this.saleDate = data.sale_date || data.saleDate || data.date;
    this.createdAt = data.created_at || data.createdAt;
    this.updatedAt = data.updated_at || data.updatedAt;
    this.items = data.items || [];
  }

  static fromRow(row) {
    return new Sale({
      id: row.id,
      sale_number: row.sale_number,
      customer_id: row.customer_id,
      customer_name: row.customer_name,
      customer_email: row.customer_email,
      customer_phone: row.customer_phone,
      total_amount: row.total_amount,
      tax_amount: row.tax_amount,
      discount_amount: row.discount_amount,
      payment_method: row.payment_method,
      payment_status: row.payment_status,
      sales_person: row.sales_person,
      notes: row.notes,
      sale_date: row.sale_date,
      created_at: row.created_at,
      updated_at: row.updated_at
    });
  }

  toJSON() {
    return {
      _id: this.id,
      id: this.id,
      saleNumber: this.saleNumber,
      customer: {
        name: this.customerName,
        email: this.customerEmail,
        phone: this.customerPhone
      },
      totalAmount: this.totalAmount,
      taxAmount: this.taxAmount,
      discountAmount: this.discountAmount,
      paymentMethod: this.paymentMethod,
      paymentStatus: this.paymentStatus,
      salesPerson: this.salesPerson,
      notes: this.notes,
      date: this.saleDate,
      items: this.items,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  static find(conditions = {}) {
    const queryBuilder = {
      conditions,
      sortField: null,
      sortOrder: 'DESC',
      limitValue: null,
      skipValue: 0,

      sort: function(sortOptions) {
        if (typeof sortOptions === 'object') {
          const field = Object.keys(sortOptions)[0];
          this.sortField = field === 'date' ? 'sale_date' : field;
          this.sortOrder = sortOptions[field] === -1 ? 'DESC' : 'ASC';
        }
        return this;
      },

      limit: function(value) {
        this.limitValue = parseInt(value);
        return this;
      },

      skip: function(value) {
        this.skipValue = parseInt(value);
        return this;
      },

      populate: function(field) {
        return this; // Placeholder for Mongoose compatibility
      },

      exec: async function() {
        try {
          let query = 'SELECT * FROM sales WHERE 1=1';
          const params = [];

          if (this.sortField) {
            query += ` ORDER BY ${this.sortField} ${this.sortOrder}`;
          } else {
            query += ' ORDER BY sale_date DESC';
          }

          if (this.limitValue) {
            query += ' LIMIT ?';
            params.push(this.limitValue);

            if (this.skipValue > 0) {
              query += ' OFFSET ?';
              params.push(this.skipValue);
            }
          }

          const rows = await executeQuery(query, params);
          return rows.map(row => Sale.fromRow(row));
        } catch (error) {
          console.error('Error finding sales:', error);
          return [];
        }
      }
    };

    queryBuilder.then = function(resolve, reject) {
      return this.exec().then(resolve, reject);
    };

    return queryBuilder;
  }

  static async countDocuments(conditions = {}) {
    try {
      const rows = await executeQuery('SELECT COUNT(*) as count FROM sales');
      return rows[0].count;
    } catch (error) {
      console.error('Error counting sales:', error);
      return 0;
    }
  }

  static async findById(id) {
    try {
      const rows = await executeQuery('SELECT * FROM sales WHERE id = ?', [id]);
      return rows.length > 0 ? Sale.fromRow(rows[0]) : null;
    } catch (error) {
      console.error('Error finding sale by ID:', error);
      return null;
    }
  }
}

module.exports = Sale;
