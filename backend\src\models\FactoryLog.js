const { executeQuery } = require('../config/database');

class FactoryLog {
  static find(conditions = {}) {
    const queryBuilder = {
      sort: () => queryBuilder,
      limit: () => queryBuilder,
      skip: () => queryBuilder,
      populate: () => queryBuilder,
      exec: async () => [],
      then: (resolve) => resolve([])
    };
    return queryBuilder;
  }

  static async countDocuments() { return 0; }
  static async findById() { return null; }
}

// Temporary placeholder - replace with full MySQL implementation
// const mongoose = require('mongoose');

const factoryLogSchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  type: {
    type: String,
    enum: ['production', 'consumption', 'waste'],
    required: true
  },
  materialsUsed: [{
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 0
    },
    unit: {
      type: String,
      required: true
    },
    cost: {
      type: Number,
      min: 0
    }
  }],
  productsGenerated: [{
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 0
    },
    unit: {
      type: String,
      required: true
    },
    estimatedValue: {
      type: Number,
      min: 0
    }
  }],
  batchNumber: {
    type: String,
    trim: true
  },
  operator: {
    type: String,
    trim: true
  },
  shift: {
    type: String,
    enum: ['morning', 'afternoon', 'night'],
    default: 'morning'
  },
  notes: {
    type: String,
    trim: true
  },
  qualityCheck: {
    passed: {
      type: Boolean,
      default: true
    },
    notes: {
      type: String,
      trim: true
    },
    inspector: {
      type: String,
      trim: true
    }
  },
  efficiency: {
    type: Number,
    min: 0,
    max: 100 // Percentage
  },
  totalCost: {
    type: Number,
    min: 0
  },
  totalValue: {
    type: Number,
    min: 0
  }
}, {
  timestamps: true
});

// Indexes for better performance
factoryLogSchema.index({ date: -1 });
factoryLogSchema.index({ type: 1 });
factoryLogSchema.index({ batchNumber: 1 });
factoryLogSchema.index({ 'materialsUsed.product': 1 });
factoryLogSchema.index({ 'productsGenerated.product': 1 });

// Virtual for profit/loss
factoryLogSchema.virtual('profitLoss').get(function() {
  if (this.totalValue && this.totalCost) {
    return this.totalValue - this.totalCost;
  }
  return null;
});

// Pre-save middleware to calculate totals
factoryLogSchema.pre('save', function(next) {
  // Calculate total cost from materials used
  this.totalCost = this.materialsUsed.reduce((total, material) => {
    return total + (material.cost || 0);
  }, 0);

  // Calculate total value from products generated
  this.totalValue = this.productsGenerated.reduce((total, product) => {
    return total + (product.estimatedValue || 0);
  }, 0);

  next();
});

factoryLogSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('FactoryLog', factoryLogSchema);
