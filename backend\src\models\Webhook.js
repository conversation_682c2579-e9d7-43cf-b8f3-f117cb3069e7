const { executeQuery } = require('../config/database');

class Webhook {
  static find(conditions = {}) {
    const queryBuilder = {
      sort: () => queryBuilder,
      limit: () => queryBuilder,
      skip: () => queryBuilder,
      populate: () => queryBuilder,
      exec: async () => [],
      then: (resolve) => resolve([])
    };
    return queryBuilder;
  }

  static async countDocuments() { return 0; }
  static async findById() { return null; }
}

// Temporary placeholder - replace with full MySQL implementation
// const mongoose = require('mongoose');

const webhookSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  url: {
    type: String,
    required: true,
    trim: true
  },
  events: [{
    type: String,
    enum: [
      'stock.low',
      'stock.out',
      'stock.updated',
      'order.created',
      'order.updated',
      'order.completed',
      'sale.created',
      'production.completed',
      'production.started'
    ],
    required: true
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  secret: {
    type: String,
    trim: true
  },
  headers: {
    type: Map,
    of: String,
    default: new Map()
  },
  retryPolicy: {
    maxRetries: {
      type: Number,
      default: 3,
      min: 0,
      max: 10
    },
    retryDelay: {
      type: Number,
      default: 1000, // milliseconds
      min: 100
    }
  },
  lastTriggered: {
    type: Date
  },
  lastStatus: {
    type: String,
    enum: ['success', 'failed', 'pending']
  },
  lastError: {
    type: String,
    trim: true
  },
  successCount: {
    type: Number,
    default: 0,
    min: 0
  },
  failureCount: {
    type: Number,
    default: 0,
    min: 0
  }
}, {
  timestamps: true
});

// Indexes for better performance
webhookSchema.index({ events: 1 });
webhookSchema.index({ isActive: 1 });
webhookSchema.index({ lastTriggered: -1 });

// Virtual for success rate
webhookSchema.virtual('successRate').get(function() {
  const total = this.successCount + this.failureCount;
  if (total === 0) return 0;
  return ((this.successCount / total) * 100).toFixed(2);
});

webhookSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Webhook', webhookSchema);
