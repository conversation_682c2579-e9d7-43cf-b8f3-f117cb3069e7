const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'jussamy_inventory',
  charset: 'utf8mb4',
  timezone: '+00:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

let connection = null;

/**
 * Create database connection
 */
async function connectDB() {
  try {
    console.log('🔄 Connecting to MySQL database...');
    
    // First connect without database to create it if it doesn't exist
    const tempConnection = await mysql.createConnection({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      password: dbConfig.password,
      charset: dbConfig.charset
    });

    // Create database if it doesn't exist
    await tempConnection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbConfig.database}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    await tempConnection.end();

    // Now connect to the specific database
    connection = await mysql.createConnection(dbConfig);
    
    console.log('✅ MySQL database connected successfully');
    console.log(`📊 Database: ${dbConfig.database}`);
    console.log(`🏠 Host: ${dbConfig.host}:${dbConfig.port}`);
    
    // Initialize database schema
    await initializeSchema();
    
    return connection;
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

/**
 * Initialize database schema
 */
async function initializeSchema() {
  try {
    console.log('🔄 Initializing database schema...');
    
    // Read and execute schema file
    const schemaPath = path.join(__dirname, '../sql/schema.sql');
    if (fs.existsSync(schemaPath)) {
      const schema = fs.readFileSync(schemaPath, 'utf8');
      const statements = schema.split(';').filter(stmt => stmt.trim());
      
      for (const statement of statements) {
        if (statement.trim()) {
          await connection.execute(statement);
        }
      }
      console.log('✅ Database schema initialized');
    }
    
    // Insert sample data if tables are empty
    await insertSampleData();
    
  } catch (error) {
    console.error('❌ Schema initialization error:', error.message);
    throw error;
  }
}

/**
 * Insert sample data if tables are empty
 */
async function insertSampleData() {
  try {
    // Check if categories table has data
    const [categoryRows] = await connection.execute('SELECT COUNT(*) as count FROM categories');
    
    if (categoryRows[0].count === 0) {
      console.log('🔄 Inserting sample data...');
      
      const sampleDataPath = path.join(__dirname, '../sql/sample_data.sql');
      if (fs.existsSync(sampleDataPath)) {
        const sampleData = fs.readFileSync(sampleDataPath, 'utf8');
        const statements = sampleData.split(';').filter(stmt => stmt.trim());
        
        for (const statement of statements) {
          if (statement.trim()) {
            await connection.execute(statement);
          }
        }
        console.log('✅ Sample data inserted');
      }
    }
  } catch (error) {
    console.error('❌ Sample data insertion error:', error.message);
  }
}

/**
 * Get database connection
 */
function getConnection() {
  if (!connection) {
    throw new Error('Database not connected. Call connectDB() first.');
  }
  return connection;
}

/**
 * Execute query with error handling
 */
async function executeQuery(query, params = []) {
  try {
    const conn = getConnection();
    const [rows] = await conn.execute(query, params);
    return rows;
  } catch (error) {
    console.error('❌ Query execution error:', error.message);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
}

/**
 * Execute transaction
 */
async function executeTransaction(queries) {
  const conn = getConnection();
  await conn.beginTransaction();
  
  try {
    const results = [];
    for (const { query, params } of queries) {
      const [rows] = await conn.execute(query, params || []);
      results.push(rows);
    }
    
    await conn.commit();
    return results;
  } catch (error) {
    await conn.rollback();
    throw error;
  }
}

/**
 * Close database connection
 */
async function closeConnection() {
  if (connection) {
    await connection.end();
    connection = null;
    console.log('✅ Database connection closed');
  }
}

/**
 * Health check
 */
async function healthCheck() {
  try {
    const conn = getConnection();
    await conn.execute('SELECT 1');
    return { status: 'healthy', database: dbConfig.database };
  } catch (error) {
    return { status: 'unhealthy', error: error.message };
  }
}

module.exports = {
  connectDB,
  getConnection,
  executeQuery,
  executeTransaction,
  closeConnection,
  healthCheck,
  dbConfig
};
