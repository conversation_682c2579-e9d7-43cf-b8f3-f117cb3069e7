const { executeQuery } = require('../config/database');

class Category {
  constructor(data = {}) {
    this.id = data.id;
    this.name = data.name;
    this.description = data.description;
    this.unitType = data.unit_type || data.unitType;
    this.unitLabel = data.unit_label || data.unitLabel;
    this.isActive = data.is_active !== undefined ? data.is_active : data.isActive;
    this.createdAt = data.created_at || data.createdAt;
    this.updatedAt = data.updated_at || data.updatedAt;
  }

  // Convert database row to Category instance
  static fromRow(row) {
    return new Category({
      id: row.id,
      name: row.name,
      description: row.description,
      unit_type: row.unit_type,
      unit_label: row.unit_label,
      is_active: row.is_active,
      created_at: row.created_at,
      updated_at: row.updated_at
    });
  }

  // Convert to API response format (Mongoose compatibility)
  toJSON() {
    return {
      _id: this.id,
      id: this.id,
      name: this.name,
      description: this.description,
      unitType: this.unitType,
      unitLabel: this.unitLabel,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  // Find all categories - returns a query-like object for Mongoose compatibility
  static find(conditions = {}) {
    const queryBuilder = {
      conditions,
      sortField: null,
      sortOrder: 'ASC',
      limitValue: null,
      skipValue: 0,

      sort: function(sortOptions) {
        if (typeof sortOptions === 'string') {
          this.sortField = sortOptions.replace('-', '');
          this.sortOrder = sortOptions.startsWith('-') ? 'DESC' : 'ASC';
        } else if (typeof sortOptions === 'object') {
          const field = Object.keys(sortOptions)[0];
          this.sortField = field;
          this.sortOrder = sortOptions[field] === -1 ? 'DESC' : 'ASC';
        }
        return this;
      },

      limit: function(value) {
        this.limitValue = parseInt(value);
        return this;
      },

      skip: function(value) {
        this.skipValue = parseInt(value);
        return this;
      },

      exec: async function() {
        try {
          let query = 'SELECT * FROM categories WHERE 1=1';
          const params = [];

          if (this.conditions.isActive !== undefined) {
            query += ' AND is_active = ?';
            params.push(this.conditions.isActive ? 1 : 0);
          }

          if (this.sortField) {
            query += ` ORDER BY ${this.sortField} ${this.sortOrder}`;
          } else {
            query += ' ORDER BY name ASC';
          }

          if (this.limitValue) {
            query += ' LIMIT ?';
            params.push(this.limitValue);

            if (this.skipValue > 0) {
              query += ' OFFSET ?';
              params.push(this.skipValue);
            }
          }

          const rows = await executeQuery(query, params);
          return rows.map(row => Category.fromRow(row));
        } catch (error) {
          console.error('Error finding categories:', error);
          return [];
        }
      }
    };

    // For direct await usage
    queryBuilder.then = function(resolve, reject) {
      return this.exec().then(resolve, reject);
    };

    return queryBuilder;
  }

  // Find category by ID
  static async findById(id) {
    try {
      const rows = await executeQuery('SELECT * FROM categories WHERE id = ?', [id]);
      return rows.length > 0 ? Category.fromRow(rows[0]) : null;
    } catch (error) {
      console.error('Error finding category by ID:', error);
      return null;
    }
  }

  // Create new category
  static async create(categoryData) {
    try {
      const result = await executeQuery(
        'INSERT INTO categories (name, description, unit_type, unit_label, is_active) VALUES (?, ?, ?, ?, ?)',
        [
          categoryData.name,
          categoryData.description || null,
          categoryData.unitType || 'unit',
          categoryData.unitLabel || 'Units',
          categoryData.isActive !== undefined ? categoryData.isActive : true
        ]
      );

      return Category.fromRow({
        id: result.insertId,
        name: categoryData.name,
        description: categoryData.description,
        unit_type: categoryData.unitType || 'unit',
        unit_label: categoryData.unitLabel || 'Units',
        is_active: categoryData.isActive !== undefined ? categoryData.isActive : true,
        created_at: new Date(),
        updated_at: new Date()
      });
    } catch (error) {
      console.error('Error creating category:', error);
      throw error;
    }
  }

  // Update category
  async save() {
    try {
      if (!this.id) {
        throw new Error('Cannot update category without ID');
      }

      await executeQuery(
        'UPDATE categories SET name = ?, description = ?, unit_type = ?, unit_label = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [this.name, this.description, this.unitType, this.unitLabel, this.isActive, this.id]
      );

      return this;
    } catch (error) {
      console.error('Error updating category:', error);
      throw error;
    }
  }

  // Delete category
  static async findByIdAndDelete(id) {
    try {
      const category = await Category.findById(id);
      if (!category) return null;

      await executeQuery('DELETE FROM categories WHERE id = ?', [id]);
      return category;
    } catch (error) {
      console.error('Error deleting category:', error);
      throw error;
    }
  }

  // Count categories
  static async countDocuments(conditions = {}) {
    try {
      let query = 'SELECT COUNT(*) as count FROM categories WHERE 1=1';
      const params = [];

      if (conditions.isActive !== undefined) {
        query += ' AND is_active = ?';
        params.push(conditions.isActive ? 1 : 0);
      }

      const rows = await executeQuery(query, params);
      return rows[0].count;
    } catch (error) {
      console.error('Error counting categories:', error);
      return 0;
    }
  }
}

module.exports = Category;
