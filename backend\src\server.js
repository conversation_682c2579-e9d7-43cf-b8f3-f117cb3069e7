const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:3000",
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use('/api/', limiter);

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/jussamy', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Database connection error:', error);
    console.log('⚠️ Running without database connection. Some features may not work.');
    // Don't exit, allow server to start for testing
  }
};

// Routes - Load them conditionally to identify issues
try {
  app.use('/api/categories', require('./routes/categories'));
  console.log('✅ Categories routes loaded');
} catch (err) {
  console.error('❌ Categories routes error:', err.message);
}

try {
  app.use('/api/products', require('./routes/products'));
  console.log('✅ Products routes loaded');
} catch (err) {
  console.error('❌ Products routes error:', err.message);
}

try {
  app.use('/api/factory', require('./routes/factory'));
  console.log('✅ Factory routes loaded');
} catch (err) {
  console.error('❌ Factory routes error:', err.message);
}

try {
  app.use('/api/sales', require('./routes/sales'));
  console.log('✅ Sales routes loaded');
} catch (err) {
  console.error('❌ Sales routes error:', err.message);
}

try {
  app.use('/api/orders', require('./routes/orders'));
  console.log('✅ Orders routes loaded');
} catch (err) {
  console.error('❌ Orders routes error:', err.message);
}

try {
  app.use('/api/webhooks', require('./routes/webhooks'));
  console.log('✅ Webhooks routes loaded');
} catch (err) {
  console.error('❌ Webhooks routes error:', err.message);
}

try {
  app.use('/api/unity', require('./routes/unity'));
  console.log('✅ Unity routes loaded');
} catch (err) {
  console.error('❌ Unity routes error:', err.message);
}

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Make io available to routes
app.set('io', io);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'production' ? {} : err.stack
  });
});

// 404 handler - commented out temporarily
// app.use('*', (req, res) => {
//   res.status(404).json({ message: 'Route not found' });
// });

const PORT = process.env.PORT || 5000;

// Start server
const startServer = async () => {
  await connectDB();
  server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  });
};

startServer();

module.exports = { app, io };
