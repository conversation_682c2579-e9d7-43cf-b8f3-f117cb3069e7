const express = require('express');
const router = express.Router();
const { Category } = require('../models');
const { body, validationResult } = require('express-validator');

// Validation middleware
const validateCategory = [
  body('name').trim().notEmpty().withMessage('Category name is required'),
  body('unitType').isIn(['weight', 'unit']).withMessage('Unit type must be either weight or unit'),
  body('unitLabel').trim().notEmpty().withMessage('Unit label is required'),
  body('customAttributes').optional().isArray().withMessage('Custom attributes must be an array')
];

// GET /api/categories - Get all categories
router.get('/', async (req, res) => {
  try {
    const { isActive = true, page = 1, limit = 50 } = req.query;
    
    const filter = {};
    if (isActive !== 'all') {
      filter.isActive = isActive === 'true';
    }

    const categories = await Category.find(filter)
      .sort({ name: 1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Category.countDocuments(filter);

    res.json({
      categories,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Error fetching categories', error: error.message });
  }
});

// GET /api/categories/:id - Get category by ID
router.get('/:id', async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }
    res.json(category);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching category', error: error.message });
  }
});

// POST /api/categories - Create new category
router.post('/', validateCategory, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const category = await Category.create(req.body);

    // Emit socket event for real-time updates
    const io = req.app.get('io');
    io.emit('category:created', category);

    res.status(201).json(category);
  } catch (error) {
    if (error.code === 11000) {
      return res.status(400).json({ message: 'Category name already exists' });
    }
    res.status(500).json({ message: 'Error creating category', error: error.message });
  }
});

// PUT /api/categories/:id - Update category
router.put('/:id', validateCategory, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const category = await Category.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );

    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }

    // Emit socket event for real-time updates
    const io = req.app.get('io');
    io.emit('category:updated', category);

    res.json(category);
  } catch (error) {
    if (error.code === 11000) {
      return res.status(400).json({ message: 'Category name already exists' });
    }
    res.status(500).json({ message: 'Error updating category', error: error.message });
  }
});

// DELETE /api/categories/:id - Delete category (soft delete)
router.delete('/:id', async (req, res) => {
  try {
    const category = await Category.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );

    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }

    // Emit socket event for real-time updates
    const io = req.app.get('io');
    io.emit('category:deleted', { id: req.params.id });

    res.json({ message: 'Category deactivated successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Error deleting category', error: error.message });
  }
});

module.exports = router;
