// @ts-nocheck
import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  TextField,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  Alert,
  CircularProgress,
  Grid
} from '@mui/material';
import {
  Add as AddIcon,
  Warning as WarningIcon,
  TrendingDown as TrendingDownIcon,
  Inventory as InventoryIcon
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { productsAPI, categoriesAPI } from '../services/api';

interface Product {
  _id: string;
  name: string;
  category: {
    _id: string;
    name: string;
    unitType: string;
    unitLabel: string;
  };
  price: number;
  currentStock: number;
  minStockLevel: number;
  stockStatus: 'low' | 'normal' | 'high' | 'out';
  isActive: boolean;
  isFinishedProduct: boolean;
}

interface Category {
  _id: string;
  name: string;
  unitType: string;
  unitLabel: string;
}

const ProductsTab: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [filters, setFilters] = useState({
    category: '',
    stockStatus: '',
    isFinishedProduct: ''
  });

  // Form state for product creation/editing
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    price: 0,
    currentStock: 0,
    minStockLevel: 0,
    isFinishedProduct: false
  });

  useEffect(() => {
    fetchProducts();
    fetchCategories();
  }, [filters]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await productsAPI.getAll(filters);
      setProducts(response.data.products);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch products');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await categoriesAPI.getAll();
      setCategories(response.data.categories);
    } catch (err: any) {
      console.error('Failed to fetch categories:', err);
    }
  };

  const handleCreateProduct = async () => {
    try {
      await productsAPI.create(formData);
      setOpenDialog(false);
      resetForm();
      fetchProducts();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create product');
    }
  };

  const handleUpdateProduct = async () => {
    if (!selectedProduct) return;
    
    try {
      await productsAPI.update(selectedProduct._id, formData);
      setOpenDialog(false);
      resetForm();
      fetchProducts();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update product');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      category: '',
      price: 0,
      currentStock: 0,
      minStockLevel: 0,
      isFinishedProduct: false
    });
    setSelectedProduct(null);
  };

  const openCreateDialog = () => {
    resetForm();
    setOpenDialog(true);
  };

  const openEditDialog = (product: Product) => {
    setSelectedProduct(product);
    setFormData({
      name: product.name,
      category: product.category._id,
      price: product.price,
      currentStock: product.currentStock,
      minStockLevel: product.minStockLevel,
      isFinishedProduct: product.isFinishedProduct
    });
    setOpenDialog(true);
  };

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case 'low': return 'warning';
      case 'out': return 'error';
      case 'high': return 'info';
      default: return 'success';
    }
  };

  const columns: GridColDef[] = [
    { field: 'name', headerName: 'Product Name', width: 200 },
    {
      field: 'category',
      headerName: 'Category',
      width: 150,
      valueGetter: (params: any) => params.row.category.name
    },
    {
      field: 'price',
      headerName: 'Price',
      width: 100,
      valueFormatter: (params: any) => `$${params.value.toFixed(2)}`
    },
    {
      field: 'currentStock',
      headerName: 'Current Stock',
      width: 120,
      valueFormatter: (params: any) => `${params.value} ${params.row.category.unitLabel}`
    },
    {
      field: 'minStockLevel',
      headerName: 'Min Stock',
      width: 100,
      valueFormatter: (params: any) => `${params.value} ${params.row.category.unitLabel}`
    },
    {
      field: 'stockStatus',
      headerName: 'Stock Status',
      width: 120,
      renderCell: (params: any) => (
        <Chip
          label={params.value}
          color={getStockStatusColor(params.value) as any}
          size="small"
        />
      )
    },
    {
      field: 'isFinishedProduct',
      headerName: 'Type',
      width: 120,
      renderCell: (params: any) => (
        <Chip
          label={params.value ? 'Finished' : 'Raw Material'}
          variant="outlined"
          size="small"
        />
      )
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 100,
      getActions: (params: any) => [
        <GridActionsCellItem
          icon={<AddIcon />}
          label="Edit"
          onClick={() => openEditDialog(params.row)}
          key="edit"
        />
      ]
    }
  ];

  // Calculate summary statistics
  const lowStockCount = products.filter(p => p.stockStatus === 'low').length;
  const outOfStockCount = products.filter(p => p.stockStatus === 'out').length;
  const totalProducts = products.length;
  const totalValue = products.reduce((sum, p) => sum + (p.currentStock * p.price), 0);

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Products Management
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <InventoryIcon color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{totalProducts}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Products
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <WarningIcon color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{lowStockCount}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Low Stock
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingDownIcon color="error" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{outOfStockCount}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Out of Stock
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <InventoryIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">${totalValue.toFixed(2)}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Inventory Value
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters and Actions */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Category</InputLabel>
              <Select
                value={filters.category}
                label="Category"
                onChange={(e) => setFilters({ ...filters, category: e.target.value })}
              >
                <MenuItem value="">All Categories</MenuItem>
                {categories.map((cat) => (
                  <MenuItem key={cat._id} value={cat._id}>
                    {cat.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Stock Status</InputLabel>
              <Select
                value={filters.stockStatus}
                label="Stock Status"
                onChange={(e) => setFilters({ ...filters, stockStatus: e.target.value })}
              >
                <MenuItem value="">All Status</MenuItem>
                <MenuItem value="low">Low Stock</MenuItem>
                <MenuItem value="out">Out of Stock</MenuItem>
                <MenuItem value="normal">Normal</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Product Type</InputLabel>
              <Select
                value={filters.isFinishedProduct}
                label="Product Type"
                onChange={(e) => setFilters({ ...filters, isFinishedProduct: e.target.value })}
              >
                <MenuItem value="">All Types</MenuItem>
                <MenuItem value="true">Finished Products</MenuItem>
                <MenuItem value="false">Raw Materials</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={openCreateDialog}
              fullWidth
            >
              Add Product
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Products Data Grid */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={products}
          columns={columns}
          getRowId={(row) => row._id}
          loading={loading}
          pageSizeOptions={[25, 50, 100]}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 25 },
            },
          }}
          disableRowSelectionOnClick
        />
      </Paper>

      {/* Create/Edit Product Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedProduct ? 'Edit Product' : 'Create New Product'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Product Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={formData.category}
                  label="Category"
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                >
                  {categories.map((cat) => (
                    <MenuItem key={cat._id} value={cat._id}>
                      {cat.name} ({cat.unitLabel})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Price"
                type="number"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Current Stock"
                type="number"
                value={formData.currentStock}
                onChange={(e) => setFormData({ ...formData, currentStock: parseFloat(e.target.value) || 0 })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Min Stock Level"
                type="number"
                value={formData.minStockLevel}
                onChange={(e) => setFormData({ ...formData, minStockLevel: parseFloat(e.target.value) || 0 })}
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Product Type</InputLabel>
                <Select
                  value={formData.isFinishedProduct}
                  label="Product Type"
                  onChange={(e) => setFormData({ ...formData, isFinishedProduct: e.target.value as boolean })}
                >
                  <MenuItem value={false}>Raw Material</MenuItem>
                  <MenuItem value={true}>Finished Product</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button 
            onClick={selectedProduct ? handleUpdateProduct : handleCreateProduct}
            variant="contained"
          >
            {selectedProduct ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProductsTab;
