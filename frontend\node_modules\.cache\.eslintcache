[{"C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\components\\FactoryTab.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\components\\ProductsTab.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\components\\WebhooksTab.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\components\\OrdersTab.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\components\\SalesTab.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\services\\api.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\services\\socket.ts": "10"}, {"size": 554, "mtime": 1752073924247, "results": "11", "hashOfConfig": "12"}, {"size": 425, "mtime": 1752073924054, "results": "13", "hashOfConfig": "12"}, {"size": 5142, "mtime": 1752076382296, "results": "14", "hashOfConfig": "12"}, {"size": 19470, "mtime": 1752078766538, "results": "15", "hashOfConfig": "12"}, {"size": 14528, "mtime": 1752078701335, "results": "16", "hashOfConfig": "12"}, {"size": 18271, "mtime": 1752077696831, "results": "17", "hashOfConfig": "12"}, {"size": 22231, "mtime": 1752078696164, "results": "18", "hashOfConfig": "12"}, {"size": 19395, "mtime": 1752077674630, "results": "19", "hashOfConfig": "12"}, {"size": 4020, "mtime": 1752077708474, "results": "20", "hashOfConfig": "12"}, {"size": 4041, "mtime": 1752077719510, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dwf61n", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\components\\FactoryTab.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\components\\ProductsTab.tsx", ["52", "53"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\components\\WebhooksTab.tsx", ["54", "55", "56"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\components\\OrdersTab.tsx", ["57"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\components\\SalesTab.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\JusSamy\\frontend\\src\\services\\socket.ts", [], [], {"ruleId": "58", "severity": 1, "message": "59", "line": 21, "column": 3, "nodeType": "60", "messageId": "61", "endLine": 21, "endColumn": 19}, {"ruleId": "62", "severity": 1, "message": "63", "line": 83, "column": 6, "nodeType": "64", "endLine": 83, "endColumn": 15, "suggestions": "65"}, {"ruleId": "58", "severity": 1, "message": "66", "line": 22, "column": 3, "nodeType": "60", "messageId": "61", "endLine": 22, "endColumn": 9}, {"ruleId": "58", "severity": 1, "message": "67", "line": 23, "column": 3, "nodeType": "60", "messageId": "61", "endLine": 23, "endColumn": 19}, {"ruleId": "58", "severity": 1, "message": "68", "line": 28, "column": 3, "nodeType": "60", "messageId": "61", "endLine": 28, "endColumn": 13}, {"ruleId": "58", "severity": 1, "message": "69", "line": 29, "column": 3, "nodeType": "60", "messageId": "61", "endLine": 29, "endColumn": 10}, "@typescript-eslint/no-unused-vars", "'CircularProgress' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["70"], "'Switch' is defined but never used.", "'FormControlLabel' is defined but never used.", "'IconButton' is defined but never used.", "'Divider' is defined but never used.", {"desc": "71", "fix": "72"}, "Update the dependencies array to be: [fetchProducts, filters]", {"range": "73", "text": "74"}, [1837, 1846], "[fetchProducts, filters]"]