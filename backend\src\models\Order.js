const { executeQuery } = require('../config/database');

class Order {
  static find(conditions = {}) {
    const queryBuilder = {
      sort: () => queryBuilder,
      limit: () => queryBuilder,
      skip: () => queryBuilder,
      populate: () => queryBuilder,
      exec: async () => [],
      then: (resolve) => resolve([])
    };
    return queryBuilder;
  }

  static async countDocuments() { return 0; }
  static async findById() { return null; }
}

// Temporary placeholder - replace with full MySQL implementation
// const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    unique: true,
    required: true
  },
  orderDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  deliveryDate: {
    type: Date,
    required: true
  },
  customer: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    email: {
      type: String,
      trim: true,
      lowercase: true
    },
    phone: {
      type: String,
      required: true,
      trim: true
    },
    address: {
      type: String,
      required: true,
      trim: true
    }
  },
  items: [{
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 0
    },
    unitPrice: {
      type: Number,
      required: true,
      min: 0
    },
    totalPrice: {
      type: Number,
      required: true,
      min: 0
    },
    status: {
      type: String,
      enum: ['pending', 'confirmed', 'in_production', 'ready', 'delivered'],
      default: 'pending'
    },
    notes: {
      type: String,
      trim: true
    }
  }],
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'in_production', 'ready', 'delivered', 'cancelled'],
    default: 'pending'
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  tax: {
    rate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    amount: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  discount: {
    type: Number,
    default: 0,
    min: 0
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  paymentTerms: {
    type: String,
    enum: ['advance', 'on_delivery', 'net_30', 'net_60'],
    default: 'on_delivery'
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'partial', 'paid'],
    default: 'pending'
  },
  advancePayment: {
    amount: {
      type: Number,
      default: 0,
      min: 0
    },
    date: {
      type: Date
    },
    method: {
      type: String,
      enum: ['cash', 'card', 'bank_transfer', 'check']
    }
  },
  assignedTo: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    trim: true
  },
  stockAlert: {
    isTriggered: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      trim: true
    },
    triggeredAt: {
      type: Date
    }
  }
}, {
  timestamps: true
});

// Indexes for better performance
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ orderDate: -1 });
orderSchema.index({ deliveryDate: 1 });
orderSchema.index({ status: 1 });
orderSchema.index({ priority: 1 });
orderSchema.index({ 'customer.email': 1 });
orderSchema.index({ 'items.product': 1 });
orderSchema.index({ assignedTo: 1 });

// Virtual for days until delivery
orderSchema.virtual('daysUntilDelivery').get(function() {
  const today = new Date();
  const delivery = new Date(this.deliveryDate);
  const diffTime = delivery - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Virtual for order urgency
orderSchema.virtual('urgencyLevel').get(function() {
  const daysLeft = this.daysUntilDelivery;
  if (daysLeft < 0) return 'overdue';
  if (daysLeft <= 1) return 'urgent';
  if (daysLeft <= 3) return 'high';
  if (daysLeft <= 7) return 'medium';
  return 'low';
});

// Pre-save middleware to auto-generate order number
orderSchema.pre('save', async function(next) {
  if (this.isNew && !this.orderNumber) {
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
    
    // Find the last order of today
    const lastOrder = await this.constructor.findOne({
      orderNumber: new RegExp(`^ORD-${dateStr}-`)
    }).sort({ orderNumber: -1 });

    let sequence = 1;
    if (lastOrder) {
      const lastSequence = parseInt(lastOrder.orderNumber.split('-')[2]);
      sequence = lastSequence + 1;
    }

    this.orderNumber = `ORD-${dateStr}-${sequence.toString().padStart(4, '0')}`;
  }

  // Calculate totals
  this.subtotal = this.items.reduce((total, item) => total + item.totalPrice, 0);
  this.tax.amount = (this.subtotal * this.tax.rate) / 100;
  this.totalAmount = this.subtotal + this.tax.amount - this.discount;

  next();
});

orderSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Order', orderSchema);
